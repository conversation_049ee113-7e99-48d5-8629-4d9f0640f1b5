<?php

namespace app\common\models;

use Yii;
use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;

/**
 * Модель получателя SMS кампании
 *
 * @property int $id
 * @property int $campaign_id ID кампании
 * @property int|null $client_id ID клиента
 * @property string $phone_number Номер телефона
 * @property string|null $client_name Имя клиента
 * @property int $status Статус отправки
 * @property string|null $eskiz_message_id ID сообщения в Eskiz
 * @property string|null $error_message Сообщение об ошибке
 * @property string|null $sent_at Время отправки
 * @property string|null $delivered_at Время доставки
 * @property string $created_at
 *
 * @property SmsCampaign $campaign
 * @property Clients $client
 */
class SmsCampaignRecipient extends ActiveRecord
{
    // Статусы отправки
    const STATUS_PENDING = 0;    // Ожидание
    const STATUS_SENT = 1;       // Отправлено
    const STATUS_DELIVERED = 2;  // Доставлено
    const STATUS_FAILED = 3;     // Ошибка

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%sms_campaign_recipients}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => false,
                'value' => function() {
                    return date('Y-m-d H:i:s');
                },
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['campaign_id', 'phone_number'], 'required'],
            [['campaign_id', 'client_id', 'status'], 'integer'],
            [['error_message'], 'string'],
            [['sent_at', 'delivered_at', 'created_at'], 'safe'],
            [['phone_number'], 'string', 'max' => 20],
            [['client_name'], 'string', 'max' => 255],
            [['eskiz_message_id'], 'string', 'max' => 100],
            [['status'], 'in', 'range' => [self::STATUS_PENDING, self::STATUS_SENT, self::STATUS_DELIVERED, self::STATUS_FAILED]],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'campaign_id' => 'ID кампании',
            'client_id' => 'ID клиента',
            'phone_number' => 'Номер телефона',
            'client_name' => 'Имя клиента',
            'status' => 'Статус',
            'eskiz_message_id' => 'ID в Eskiz',
            'error_message' => 'Ошибка',
            'sent_at' => 'Отправлено',
            'delivered_at' => 'Доставлено',
            'created_at' => 'Создано',
        ];
    }

    /**
     * Получить кампанию
     */
    public function getCampaign()
    {
        return $this->hasOne(SmsCampaign::class, ['id' => 'campaign_id']);
    }

    /**
     * Получить клиента
     */
    public function getClient()
    {
        return $this->hasOne(Clients::class, ['id' => 'client_id']);
    }

    /**
     * Получить статусы отправки
     */
    public static function getStatusLabels()
    {
        return [
            self::STATUS_PENDING => 'Ожидание',
            self::STATUS_SENT => 'Отправлено',
            self::STATUS_DELIVERED => 'Доставлено',
            self::STATUS_FAILED => 'Ошибка',
        ];
    }

    /**
     * Получить метку статуса
     */
    public function getStatusLabel()
    {
        $labels = self::getStatusLabels();
        return $labels[$this->status] ?? 'Неизвестно';
    }

    /**
     * Получить CSS класс для статуса
     */
    public function getStatusClass()
    {
        $classes = [
            self::STATUS_PENDING => 'secondary',
            self::STATUS_SENT => 'info',
            self::STATUS_DELIVERED => 'success',
            self::STATUS_FAILED => 'danger',
        ];
        return $classes[$this->status] ?? 'light';
    }

    /**
     * Отметить как отправленное
     */
    public function markAsSent($eskizMessageId = null)
    {
        $this->status = self::STATUS_SENT;
        $this->eskiz_message_id = $eskizMessageId;
        $this->sent_at = date('Y-m-d H:i:s');
        return $this->save(false);
    }

    /**
     * Отметить как доставленное
     */
    public function markAsDelivered()
    {
        $this->status = self::STATUS_DELIVERED;
        $this->delivered_at = date('Y-m-d H:i:s');
        return $this->save(false);
    }

    /**
     * Отметить как неудачное
     */
    public function markAsFailed($errorMessage = null)
    {
        $this->status = self::STATUS_FAILED;
        $this->error_message = $errorMessage;
        return $this->save(false);
    }

    /**
     * Создать получателей для кампании
     */
    public static function createForCampaign($campaignId, $recipients)
    {
        $models = [];
        foreach ($recipients as $recipient) {
            $model = new self();
            $model->campaign_id = $campaignId;
            $model->client_id = $recipient['client_id'] ?? null;
            $model->phone_number = $recipient['phone'];
            $model->client_name = $recipient['name'] ?? null;
            $model->status = self::STATUS_PENDING;
            
            if ($model->save()) {
                $models[] = $model;
            }
        }
        return $models;
    }

    /**
     * Получить статистику по статусам
     */
    public static function getStatusStats($campaignId = null)
    {
        $query = self::find();
        
        if ($campaignId) {
            $query->where(['campaign_id' => $campaignId]);
        }
        
        return $query->select([
            'status',
            'COUNT(*) as count'
        ])
        ->groupBy('status')
        ->asArray()
        ->all();
    }
}
