<?php

namespace app\common\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * Модель SMS шаблона
 *
 * @property int $id
 * @property string $name Название шаблона
 * @property string $template_text Текст шаблона
 * @property int $status Статус: 0-на модерации, 1-одобрен, 2-отклонен
 * @property string|null $eskiz_template_id ID шаблона в Eskiz
 * @property bool $is_default Шаблон по умолчанию
 * @property int|null $created_by Кто создал шаблон
 * @property string $created_at
 * @property string $updated_at
 * @property string|null $approved_at Время одобрения
 */
class SmsTemplate extends ActiveRecord
{
    const STATUS_PENDING = 0;
    const STATUS_APPROVED = 1;
    const STATUS_REJECTED = 2;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%sms_templates}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name', 'template_text'], 'required'],
            [['template_text'], 'string'],
            [['status', 'created_by'], 'integer'],
            [['is_default'], 'boolean'],
            [['created_at', 'updated_at', 'approved_at'], 'safe'],
            [['name'], 'string', 'max' => 255],
            [['eskiz_template_id'], 'string', 'max' => 100],
            [['status'], 'in', 'range' => [self::STATUS_PENDING, self::STATUS_APPROVED, self::STATUS_REJECTED]],
            [['template_text'], 'validateTemplateText'],
        ];
    }

    /**
     * Валидация текста шаблона
     */
    public function validateTemplateText($attribute, $params)
    {
        if (strpos($this->$attribute, '%w') === false) {
            $this->addError($attribute, 'Шаблон должен содержать %w для вставки динамического текста');
        }
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Название шаблона',
            'template_text' => 'Текст шаблона',
            'status' => 'Статус',
            'eskiz_template_id' => 'ID в Eskiz',
            'is_default' => 'По умолчанию',
            'created_by' => 'Создал',
            'created_at' => 'Создан',
            'updated_at' => 'Обновлен',
            'approved_at' => 'Одобрен',
        ];
    }

    /**
     * Получить одобренные шаблоны
     */
    public static function getApprovedTemplates()
    {
        return self::find()
            ->where(['status' => self::STATUS_APPROVED])
            ->orderBy(['is_default' => SORT_DESC, 'name' => SORT_ASC])
            ->all();
    }

    /**
     * Получить шаблон по умолчанию
     */
    public static function getDefaultTemplate()
    {
        return self::find()
            ->where(['status' => self::STATUS_APPROVED, 'is_default' => true])
            ->one();
    }

    /**
     * Получить статусы
     */
    public static function getStatusLabels()
    {
        return [
            self::STATUS_PENDING => 'На модерации',
            self::STATUS_APPROVED => 'Одобрен',
            self::STATUS_REJECTED => 'Отклонен',
        ];
    }

    /**
     * Получить метку статуса
     */
    public function getStatusLabel()
    {
        $labels = self::getStatusLabels();
        return $labels[$this->status] ?? 'Неизвестно';
    }

    /**
     * Заменить плейсхолдер в шаблоне
     */
    public function replaceContent($message)
    {
        return str_replace('%w', $message, $this->template_text);
    }

    /**
     * Проверить, является ли шаблон одобренным
     */
    public function isApproved()
    {
        return $this->status === self::STATUS_APPROVED;
    }

    /**
     * Установить как шаблон по умолчанию
     */
    public function setAsDefault()
    {
        // Сначала убираем флаг по умолчанию у всех шаблонов
        self::updateAll(['is_default' => false]);
        
        // Устанавливаем текущий шаблон как по умолчанию
        $this->is_default = true;
        return $this->save(false);
    }

    /**
     * Поведения модели
     */
    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => function() {
                    return date('Y-m-d H:i:s');
                }
            ],
            [
                'class' => \yii\behaviors\BlameableBehavior::class,
                'createdByAttribute' => 'created_by',
                'updatedByAttribute' => false,
            ],
        ];
    }
}
