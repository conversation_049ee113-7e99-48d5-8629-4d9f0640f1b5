<?php

// Тест массовой отправки SMS с использованием подтвержденного шаблона

// Подключаем Yii2
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/vendor/yiisoft/yii2/Yii.php';

// Загружаем конфигурацию
$config = require __DIR__ . '/config/console.php';
new yii\console\Application($config);

use app\common\models\Clients;

try {
    echo "=== Тест массовой отправки SMS с подтвержденным шаблоном ===\n\n";
    
    // Получаем клиентов
    $clients = Clients::find()
        ->where(['IS', 'deleted_at', null])
        ->andWhere(['!=', 'phone_number', ''])
        ->andWhere(['IS NOT', 'phone_number', null])
        ->limit(10)
        ->all();
    
    if (empty($clients)) {
        echo "Клиенты не найдены!\n";
        exit(1);
    }
    
    echo "Найдено клиентов: " . count($clients) . "\n";
    foreach ($clients as $client) {
        echo "- ID: {$client->id}, Имя: {$client->full_name}, Телефон: {$client->phone_number}\n";
    }
    echo "\n";
    
    // Получаем токен
    $eskizLogin = '<EMAIL>';
    $eskizPassword = 'Tt03tBBJvyeWwg59E8mmeN51U8LUK1Q40iXNiDtq';
    
    // Логинимся
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => 'https://notify.eskiz.uz/api/auth/login',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_POSTFIELDS => [
            'email' => $eskizLogin,
            'password' => $eskizPassword,
        ],
    ]);

    $response = curl_exec($curl);
    curl_close($curl);
    
    $loginData = json_decode($response, true);
    $token = $loginData['data']['token'];
    echo "Токен получен: " . substr($token, 0, 20) . "...\n\n";
    
    // Используем подтвержденный шаблон
    $approvedTemplate = "Hurmatli mijoz!\n\nSizga xizmat ko'rsatganimizdan xursandmiz va fikringiz biz uchun juda muhim. Iltimos, bizning xizmatimiz haqida qisqacha fikringizni bildiring: %w\n\nJavobingiz uchun oldindan rahmat!\n\nHurmat bilan,\nSAG GILAMLAR BAZASI jamoasi";
    
    // Заменяем %w на наше сообщение
    $ourMessage = "https://carpet.idarmon.uz/feedback";
    $finalMessage = str_replace('%w', $ourMessage, $approvedTemplate);
    
    echo "Используем шаблон:\n$finalMessage\n\n";
    
    // Формируем сообщения для batch API
    $messages = [];
    foreach ($clients as $client) {
        $phone = preg_replace('/[^\d]/', '', $client->phone_number); // Убираем все кроме цифр
        if (strlen($phone) === 12 && substr($phone, 0, 3) === '998') {
            $messages[] = [
                'user_sms_id' => (string)$client->id,
                'to' => $phone,
                'text' => $finalMessage,
            ];
        }
    }
    
    echo "Подготовлено сообщений: " . count($messages) . "\n";
    foreach ($messages as $msg) {
        echo "- ID: {$msg['user_sms_id']}, Phone: {$msg['to']}\n";
    }
    echo "\n";
    
    // Подтверждение отправки
    echo "ВНИМАНИЕ! Сейчас будет отправлено " . count($messages) . " SMS с подтвержденным шаблоном.\n";
    echo "Продолжить? (y/N): ";
    $handle = fopen("php://stdin", "r");
    $line = fgets($handle);
    fclose($handle);
    
    if (trim(strtolower($line)) !== 'y') {
        echo "Отправка отменена.\n";
        exit(0);
    }
    
    // Отправляем через batch API
    echo "\nОтправляем через batch API...\n";
    
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => 'https://notify.eskiz.uz/api/message/sms/send-batch',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 60,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_POSTFIELDS => json_encode([
            'messages' => $messages,
            'from' => 'CARPET',
        ]),
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json',
            'Accept: application/json',
        ],
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    echo "HTTP Code: $httpCode\n";
    echo "Response: $response\n\n";
    
    $responseData = json_decode($response, true);
    
    if ($httpCode === 200 && isset($responseData['status']) && $responseData['status'] === true) {
        echo "✅ Массовая отправка SMS успешна!\n";
        echo "Отправлено: " . count($messages) . " SMS\n";
        
        if (isset($responseData['data'])) {
            echo "Детали:\n";
            echo json_encode($responseData['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        }
    } else {
        echo "❌ Ошибка при отправке SMS\n";
        echo "Ответ: $response\n";
    }
    
    echo "\n=== Тест завершен ===\n";
    
} catch (Exception $e) {
    echo "ОШИБКА: " . $e->getMessage() . "\n";
    echo "Трассировка:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
