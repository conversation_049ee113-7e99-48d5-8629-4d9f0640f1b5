<?php

// Проверка шаблонов в Eskiz

// Подключаем Yii2
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/vendor/yiisoft/yii2/Yii.php';

// Загружаем конфигурацию
$config = require __DIR__ . '/config/console.php';
new yii\console\Application($config);

use app\common\services\EskizBulkSmsService;

try {
    echo "=== Проверка шаблонов Eskiz ===\n\n";
    
    $eskizBulkSmsService = new EskizBulkSmsService();
    
    // Получаем токен
    $reflection = new \ReflectionClass($eskizBulkSmsService);
    $loginMethod = $reflection->getMethod('login');
    $loginMethod->setAccessible(true);
    $loginResponse = $loginMethod->invoke($eskizBulkSmsService);
    
    $token = $loginResponse['data']['token'];
    echo "Токен получен: " . substr($token, 0, 20) . "...\n\n";
    
    // Получаем шаблоны
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => 'https://notify.eskiz.uz/api/user/templates',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $token,
        ],
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    echo "HTTP Code: $httpCode\n";
    echo "Response: $response\n\n";
    
    $templatesData = json_decode($response, true);
    
    if (isset($templatesData['result']) && is_array($templatesData['result'])) {
        echo "Найдено шаблонов: " . count($templatesData['result']) . "\n\n";
        
        foreach ($templatesData['result'] as $index => $template) {
            echo "Шаблон #" . ($index + 1) . ":\n";
            echo "- ID: " . ($template['id'] ?? 'N/A') . "\n";
            echo "- Статус: " . ($template['status'] ?? 'N/A') . "\n";
            echo "- Текст: " . ($template['template'] ?? 'N/A') . "\n";
            echo "- Создан: " . ($template['created_at'] ?? 'N/A') . "\n";
            echo "\n";
        }
        
        // Ищем подтвержденные шаблоны
        $approvedTemplates = array_filter($templatesData['result'], function($template) {
            return isset($template['status']) && $template['status'] === 'reklama';
        });
        
        echo "Подтвержденных шаблонов: " . count($approvedTemplates) . "\n";
        
        if (!empty($approvedTemplates)) {
            echo "Подтвержденные шаблоны:\n";
            foreach ($approvedTemplates as $template) {
                echo "- " . $template['template'] . "\n";
            }
        }
        
    } else {
        echo "Шаблоны не найдены или ошибка в ответе\n";
    }
    
    // Тестируем отправку без шаблона
    echo "\n=== Тест отправки без шаблона ===\n";
    
    $testMessage = "Test message without template";
    $testPhone = "998930960195"; // Без +
    
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => 'https://notify.eskiz.uz/api/message/sms/send',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_POSTFIELDS => [
            'mobile_phone' => $testPhone,
            'message' => $testMessage,
            'from' => 'CARPET',
        ],
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $token,
            'Content-Type: multipart/form-data',
        ],
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    echo "Single SMS HTTP Code: $httpCode\n";
    echo "Single SMS Response: $response\n\n";
    
    // Тестируем batch API
    echo "=== Тест batch API ===\n";
    
    $messages = [
        [
            'user_sms_id' => '1',
            'to' => '998930960195',
            'text' => 'Test batch message 1',
        ],
        [
            'user_sms_id' => '2',
            'to' => '998903374146',
            'text' => 'Test batch message 2',
        ]
    ];
    
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => 'https://notify.eskiz.uz/api/message/sms/send-batch',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 60,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_POSTFIELDS => json_encode([
            'messages' => $messages,
            'from' => 'CARPET',
        ]),
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json',
            'Accept: application/json',
        ],
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    echo "Batch SMS HTTP Code: $httpCode\n";
    echo "Batch SMS Response: $response\n";
    
} catch (Exception $e) {
    echo "ОШИБКА: " . $e->getMessage() . "\n";
    echo "Трассировка:\n" . $e->getTraceAsString() . "\n";
}
