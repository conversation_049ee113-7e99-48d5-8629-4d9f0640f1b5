<?php

// Тест массовой отправки SMS с исправленным API

// Подключаем Yii2
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/vendor/yiisoft/yii2/Yii.php';

// Загружаем конфигурацию
$config = require __DIR__ . '/config/console.php';
new yii\console\Application($config);

use app\common\models\Clients;
use app\common\services\EskizBulkSmsService;

try {
    echo "=== Тест массовой отправки SMS с исправленным API ===\n\n";
    
    // 1. Получаем клиентов из базы данных
    echo "1. Получаем клиентов из базы данных...\n";
    $clients = Clients::find()
        ->where(['IS', 'deleted_at', null])
        ->andWhere(['!=', 'phone_number', ''])
        ->andWhere(['IS NOT', 'phone_number', null])
        ->limit(10)
        ->all();
    
    if (empty($clients)) {
        echo "Клиенты не найдены!\n";
        exit(1);
    }
    
    echo "Найдено клиентов: " . count($clients) . "\n";
    foreach ($clients as $client) {
        echo "- ID: {$client->id}, Имя: {$client->full_name}, Телефон: {$client->phone_number}\n";
    }
    echo "\n";
    
    // 2. Создаем сервис для массовой отправки
    echo "2. Инициализируем сервис массовой отправки SMS...\n";
    $eskizBulkSmsService = new EskizBulkSmsService();
    
    // 3. Проверяем доступность API
    echo "3. Проверяем доступность API Eskiz...\n";
    $serviceAvailable = $eskizBulkSmsService->isServiceAvailable();
    echo "API доступен: " . ($serviceAvailable ? 'ДА' : 'НЕТ') . "\n\n";
    
    if (!$serviceAvailable) {
        echo "API недоступен. Завершаем тест.\n";
        exit(1);
    }
    
    // 4. Формируем список получателей
    echo "4. Формируем список получателей...\n";
    $recipients = [];
    foreach ($clients as $client) {
        $phone = $eskizBulkSmsService->formatPhone($client->phone_number);
        
        if ($eskizBulkSmsService->validatePhone($phone)) {
            $recipients[] = [
                'client_id' => $client->id,
                'phone' => $phone,
                'name' => $client->full_name ?: 'Клиент',
            ];
            echo "✓ Добавлен: {$client->full_name} ({$phone})\n";
        } else {
            echo "✗ Пропущен (неверный номер): {$client->full_name} ({$client->phone_number})\n";
        }
    }
    
    if (empty($recipients)) {
        echo "Нет валидных получателей!\n";
        exit(1);
    }
    
    echo "\nВалидных получателей: " . count($recipients) . "\n\n";
    
    // 5. Отправляем тестовое сообщение С ШАБЛОНОМ
    echo "5. Отправляем тестовое сообщение С ШАБЛОНОМ...\n";
    $testMessage = "https://carpet.idarmon.uz/feedback?time=" . date('H:i:s');
    echo "Текст сообщения (будет вставлен в шаблон): $testMessage\n\n";
    
    // Подтверждение отправки
    echo "ВНИМАНИЕ! Сейчас будет отправлено " . count($recipients) . " SMS С ШАБЛОНОМ.\n";
    echo "Продолжить? (y/N): ";
    $handle = fopen("php://stdin", "r");
    $line = fgets($handle);
    fclose($handle);
    
    if (trim(strtolower($line)) !== 'y') {
        echo "Отправка отменена.\n";
        exit(0);
    }
    
    // Отправляем SMS С ШАБЛОНОМ
    echo "\n6. Выполняем массовую отправку SMS С ШАБЛОНОМ...\n";
    $startTime = microtime(true);
    
    $result = $eskizBulkSmsService->sendBulkSms($testMessage, $recipients, true); // true = с шаблоном
    
    $endTime = microtime(true);
    $duration = round($endTime - $startTime, 2);
    
    echo "Результат отправки:\n";
    echo "- Успешно: " . ($result['success'] ? 'ДА' : 'НЕТ') . "\n";
    echo "- Отправлено: " . ($result['sent_count'] ?? 0) . " SMS\n";
    echo "- Время выполнения: {$duration} сек\n";
    echo "- Сообщение: " . ($result['message'] ?? 'Нет сообщения') . "\n";
    
    if (isset($result['result'])) {
        echo "\nДетали ответа от API:\n";
        echo json_encode($result['result'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    }
    
    echo "\n=== Тест завершен ===\n";
    
} catch (Exception $e) {
    echo "ОШИБКА: " . $e->getMessage() . "\n";
    echo "Трассировка:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
