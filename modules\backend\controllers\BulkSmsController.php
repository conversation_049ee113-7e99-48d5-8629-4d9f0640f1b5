<?php

namespace app\modules\backend\controllers;

use app\common\models\Clients;
use app\common\models\Region;
use app\common\models\SmsCampaign;
use app\common\models\SmsCampaignRecipient;
use app\common\models\SmsTemplate;
use app\common\services\EskizBulkSmsService;
use Yii;
use yii\filters\AccessControl;
use yii\web\Controller;
use yii\web\Response;

/**
 * Контроллер для управления массовыми SMS рассылками
 */
class BulkSmsController extends BaseController
{


    /**
     * Главная страница массовых SMS
     */
    public function actionIndex()
    {
        $eskizBulkSmsService = new EskizBulkSmsService();

        // Проверяем доступность API Eskiz
        $serviceAvailable = $eskizBulkSmsService->isServiceAvailable();

        // Получаем регионы для фильтров
        $regions = Region::find()
            ->where(['deleted_at' => null])
            ->orderBy('name')
            ->all();

        return $this->render('index', [
            'serviceAvailable' => $serviceAvailable,
            'regions' => $regions,
        ]);
    }

    /**
     * Создание новой кампании
     */
    public function actionCreateCampaign()
    {
        if (!Yii::$app->request->isAjax) {
            return ['status' => 'error', 'message' => 'Ajax request is required'];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $request = Yii::$app->request;
            $name = $request->post('name');
            $message = $request->post('message');
            $clientIds = $request->post('client_ids', []);
            $useTemplate = (bool)$request->post('use_template', true);
            $templateId = $request->post('template_id');

            if (empty($name)) {
                return [
                    'status' => 'error',
                    'content' => $this->renderAjax('_campaign_form', [
                        'name' => $name,
                        'message' => $message,
                        'client_ids' => $clientIds,
                        'use_template' => $useTemplate,
                        'template_id' => $templateId,
                        'errors' => ['name' => ['Заполните название кампании']]
                    ])
                ];
            }

            if (empty($message)) {
                return [
                    'status' => 'error',
                    'content' => $this->renderAjax('_campaign_form', [
                        'name' => $name,
                        'message' => $message,
                        'client_ids' => $clientIds,
                        'use_template' => $useTemplate,
                        'template_id' => $templateId,
                        'errors' => ['message' => ['Заполните текст сообщения']]
                    ])
                ];
            }

            if (empty($clientIds)) {
                return [
                    'status' => 'error',
                    'content' => $this->renderAjax('_campaign_form', [
                        'name' => $name,
                        'message' => $message,
                        'client_ids' => $clientIds,
                        'use_template' => $useTemplate,
                        'template_id' => $templateId,
                        'errors' => ['client_ids' => ['Выберите получателей']]
                    ])
                ];
            }

            try {
                // Получаем клиентов
                $clients = Clients::find()
                    ->where(['id' => $clientIds])
                    ->andWhere(['!=', 'phone_number', ''])
                    ->andWhere(['status' => 1])
                    ->andWhere(['deleted_at' => null])
                    ->all();

                if (empty($clients)) {
                    return [
                        'status' => 'error',
                        'content' => $this->renderAjax('_campaign_form', [
                            'name' => $name,
                            'message' => $message,
                            'client_ids' => $clientIds,
                            'use_template' => $useTemplate,
                            'template_id' => $templateId,
                            'errors' => ['client_ids' => ['Не найдено активных клиентов с номерами телефонов']]
                        ])
                    ];
                }

                // Формируем список получателей
                $recipients = [];
                $eskizBulkSmsService = new EskizBulkSmsService();

                foreach ($clients as $client) {
                    $phone = $eskizBulkSmsService->formatPhone($client->phone_number);

                    if ($eskizBulkSmsService->validatePhone($phone)) {
                        $recipients[] = [
                            'client_id' => $client->id,
                            'phone' => $phone,
                            'name' => $client->full_name ?: 'Клиент',
                        ];
                    }
                }

                if (empty($recipients)) {
                    return [
                        'status' => 'error',
                        'content' => $this->renderAjax('_campaign_form', [
                            'name' => $name,
                            'message' => $message,
                            'client_ids' => $clientIds,
                            'use_template' => $useTemplate,
                            'template_id' => $templateId,
                            'errors' => ['client_ids' => ['Не найдено клиентов с валидными номерами телефонов']]
                        ])
                    ];
                }

                // Отправляем SMS с сохранением в базу данных
                $result = $eskizBulkSmsService->sendBulkSmsWithDatabase($name, $message, $recipients, $useTemplate, $templateId);

                return [
                    'status' => 'success',
                    'message' => 'SMS кампания создана и отправлена успешно',
                    'sent_count' => $result['sent_count'] ?? count($recipients),
                    'recipients_count' => count($recipients),
                ];
            } catch (\Exception $e) {
                Yii::error('Ошибка создания кампании: ' . $e->getMessage(), 'bulk-sms');
                return [
                    'status' => 'error',
                    'content' => $this->renderAjax('_campaign_form', [
                        'name' => $name,
                        'message' => $message,
                        'client_ids' => $clientIds,
                        'use_template' => $useTemplate,
                        'template_id' => $templateId,
                        'errors' => ['general' => [$e->getMessage()]]
                    ])
                ];
            }
        }

        // GET запрос - показываем форму
        return [
            'status' => 'error',
            'content' => $this->renderAjax('_campaign_form', [
                'name' => '',
                'message' => '',
                'client_ids' => [],
                'use_template' => true,
                'template_id' => null,
                'errors' => []
            ])
        ];
    }

    /**
     * Создание кампании по фильтрам
     */
    public function actionCreateCampaignByFilter()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $request = Yii::$app->request;
        $name = $request->post('name');
        $message = $request->post('message');
        $regionIds = $request->post('region_ids', []);
        $status = $request->post('status', 1);
        $useTemplate = (bool)$request->post('use_template', true);

        if (empty($message)) {
            return ['success' => false, 'message' => 'Заполните текст сообщения'];
        }

        try {
            $filters = [
                'status' => (int)$status,
                'has_phone' => true,
            ];

            if (!empty($regionIds)) {
                $filters['region_ids'] = array_map('intval', $regionIds);
            }

            $eskizBulkSmsService = new EskizBulkSmsService();
            $result = $eskizBulkSmsService->sendBulkSmsByFilter($name, $message, $filters, $useTemplate);

            return [
                'success' => true,
                'message' => 'SMS по фильтру отправлены успешно',
                'sent_count' => $result['sent_count'] ?? 0,
                'recipients_count' => $result['total_recipients'] ?? 0,
            ];
        } catch (\Exception $e) {
            Yii::error('Ошибка создания кампании по фильтру: ' . $e->getMessage(), 'bulk-sms');
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Отправка шаблона на модерацию
     */
    public function actionSubmitTemplate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $request = Yii::$app->request;
        $templateText = $request->post('template');

        if (empty($templateText)) {
            return ['success' => false, 'message' => 'Заполните текст шаблона'];
        }

        try {
            $eskizBulkSmsService = new EskizBulkSmsService();
            $result = $eskizBulkSmsService->submitTemplate($templateText);

            return [
                'success' => $result['success'],
                'message' => $result['message'],
                'result' => $result['result'] ?? null
            ];
        } catch (\Exception $e) {
            Yii::error('Ошибка отправки шаблона на модерацию: ' . $e->getMessage(), 'bulk-sms');
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Проверка статуса API Eskiz
     */
    public function actionCheckStatus()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        try {
            $eskizBulkSmsService = new EskizBulkSmsService();
            $serviceAvailable = $eskizBulkSmsService->isServiceAvailable();

            return [
                'success' => true,
                'service_available' => $serviceAvailable,
                'message' => $serviceAvailable ? 'API Eskiz доступен' : 'API Eskiz недоступен'
            ];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }



    /**
     * Поиск клиентов для рассылки
     */
    public function actionSearchClients()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $query = Yii::$app->request->get('q', '');
        $limit = (int)Yii::$app->request->get('limit', 20);

        if (strlen($query) < 2) {
            return ['results' => []];
        }

        $clients = Clients::find()
            ->where(['like', 'full_name', $query])
            ->andWhere(['!=', 'phone_number', ''])
            ->andWhere(['status' => 1])
            ->andWhere(['deleted_at' => null])
            ->limit($limit)
            ->all();

        $results = [];
        foreach ($clients as $client) {
            $results[] = [
                'id' => $client->id,
                'text' => $client->full_name . ' (' . $client->phone_number . ')',
                'phone' => $client->phone_number,
            ];
        }

        return ['results' => $results];
    }

    /**
     * Страница со статистикой SMS кампаний
     */
    public function actionCampaigns()
    {
        // Получаем кампании с пагинацией
        $campaigns = SmsCampaign::find()
            ->orderBy(['created_at' => SORT_DESC])
            ->limit(50)
            ->all();

        // Получаем общую статистику
        $totalStats = SmsCampaign::find()
            ->select([
                'COUNT(*) as total_campaigns',
                'SUM(total_recipients) as total_recipients',
                'SUM(sent_count) as total_sent',
                'SUM(delivered_count) as total_delivered',
                'SUM(failed_count) as total_failed',
            ])
            ->asArray()
            ->one();

        // Статистика по статусам
        $statusStats = SmsCampaign::find()
            ->select(['status', 'COUNT(*) as count'])
            ->groupBy('status')
            ->asArray()
            ->all();

        // Статистика по дням (последние 30 дней)
        $dailyStats = SmsCampaign::find()
            ->select([
                'DATE(created_at) as date',
                'COUNT(*) as campaigns_count',
                'SUM(sent_count) as sent_count',
            ])
            ->where(['>=', 'created_at', date('Y-m-d', strtotime('-30 days'))])
            ->groupBy('DATE(created_at)')
            ->orderBy(['date' => SORT_DESC])
            ->asArray()
            ->all();

        return $this->render('campaigns', [
            'campaigns' => $campaigns,
            'totalStats' => $totalStats,
            'statusStats' => $statusStats,
            'dailyStats' => $dailyStats,
        ]);
    }

    /**
     * Детали кампании
     */
    public function actionCampaignDetails()
    {
        if (!Yii::$app->request->isAjax) {
            return ['status' => 'error', 'message' => 'Ajax request is required'];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        $id = Yii::$app->request->get('id');
        if (!$id) {
            return ['status' => 'error', 'message' => 'ID кампании не указан'];
        }

        $campaign = SmsCampaign::findOne($id);
        if (!$campaign) {
            return ['status' => 'error', 'message' => 'Кампания не найдена'];
        }

        // Получаем получателей кампании
        $recipients = SmsCampaignRecipient::find()
            ->where(['campaign_id' => $id])
            ->orderBy(['created_at' => SORT_ASC])
            ->all();

        return [
            'status' => 'success',
            'content' => $this->renderAjax('_campaign_details', [
                'campaign' => $campaign,
                'recipients' => $recipients,
            ])
        ];
    }

    /**
     * Управление шаблонами SMS
     */
    public function actionTemplates()
    {
        $templates = SmsTemplate::find()
            ->orderBy(['is_default' => SORT_DESC, 'status' => SORT_ASC, 'created_at' => SORT_DESC])
            ->all();

        return $this->render('templates', [
            'templates' => $templates,
        ]);
    }

    /**
     * Получить доступные шаблоны для dropdown
     */
    public function actionGetTemplates()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $templates = SmsTemplate::getApprovedTemplates();
        $result = [];

        foreach ($templates as $template) {
            $result[] = [
                'id' => $template->id,
                'name' => $template->name,
                'text' => $template->template_text,
                'is_default' => $template->is_default,
            ];
        }

        return $result;
    }

    /**
     * Предварительный просмотр шаблона
     */
    public function actionPreviewTemplate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $templateId = Yii::$app->request->post('template_id');
        $message = Yii::$app->request->post('message', '');

        if (!$templateId) {
            return ['success' => false, 'message' => 'Не указан ID шаблона'];
        }

        $template = SmsTemplate::findOne($templateId);
        if (!$template) {
            return ['success' => false, 'message' => 'Шаблон не найден'];
        }

        $preview = $template->preview($message);

        return [
            'success' => true,
            'preview' => $preview,
            'template_name' => $template->name,
        ];
    }

    /**
     * Создание нового шаблона
     */
    public function actionCreateTemplate()
    {
        if (!Yii::$app->request->isAjax) {
            return ['status' => 'error', 'message' => 'Ajax request is required'];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $request = Yii::$app->request;
            $name = $request->post('name');
            $templateText = $request->post('template_text');

            if (empty($name)) {
                return [
                    'status' => 'error',
                    'content' => $this->renderAjax('_template_form', [
                        'name' => $name,
                        'template_text' => $templateText,
                        'errors' => ['name' => ['Заполните название шаблона']]
                    ])
                ];
            }

            if (empty($templateText)) {
                return [
                    'status' => 'error',
                    'content' => $this->renderAjax('_template_form', [
                        'name' => $name,
                        'template_text' => $templateText,
                        'errors' => ['template_text' => ['Заполните текст шаблона']]
                    ])
                ];
            }

            // Проверяем наличие %w в шаблоне
            if (strpos($templateText, '%w') === false) {
                return [
                    'status' => 'error',
                    'content' => $this->renderAjax('_template_form', [
                        'name' => $name,
                        'template_text' => $templateText,
                        'errors' => ['template_text' => ['Шаблон должен содержать %w для вставки динамического текста']]
                    ])
                ];
            }

            $template = new SmsTemplate();
            $template->name = $name;
            $template->template_text = $templateText;
            $template->status = SmsTemplate::STATUS_PENDING;

            if ($template->save()) {
                // Отправляем шаблон на модерацию в Eskiz
                try {
                    $eskizService = new EskizBulkSmsService();
                    $result = $eskizService->submitTemplate($templateText);

                    if ($result['success']) {
                        $template->eskiz_template_id = $result['result']['id'] ?? null;
                        $template->save(false);
                    }
                } catch (\Exception $e) {
                    Yii::warning('Не удалось отправить шаблон в Eskiz: ' . $e->getMessage(), 'bulk-sms');
                }

                return [
                    'status' => 'success',
                    'message' => 'Шаблон создан и отправлен на модерацию',
                    'template_id' => $template->id,
                ];
            } else {
                return [
                    'status' => 'error',
                    'content' => $this->renderAjax('_template_form', [
                        'name' => $name,
                        'template_text' => $templateText,
                        'errors' => ['general' => ['Ошибка создания шаблона: ' . implode(', ', $template->getFirstErrors())]]
                    ])
                ];
            }
        }

        // GET запрос - показываем форму
        return [
            'status' => 'error',
            'content' => $this->renderAjax('_template_form', [
                'name' => '',
                'template_text' => '',
                'errors' => []
            ])
        ];
    }
}
