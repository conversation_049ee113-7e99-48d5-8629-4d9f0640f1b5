<?php

use yii\helpers\Html;

/* @var $this yii\web\View */
/* @var $name string */
/* @var $template_text string */
/* @var $errors array */

?>

<form id="template-form">
    <div class="alert alert-info">
        <h5><i class="fas fa-info-circle"></i> Важно!</h5>
        <p>Шаблон должен содержать <code>%w</code> - место для вставки динамического текста.</p>
        <p><strong>Пример:</strong> "Hurmatli mijoz! %w Rahmat!"</p>
    </div>

    <div class="form-group">
        <label for="template-name">Название шаблона *</label>
        <input type="text" class="form-control <?= isset($errors['name']) ? 'is-invalid' : '' ?>" 
               id="template-name" name="name" value="<?= Html::encode($name) ?>" required 
               placeholder="Например: Рекламный шаблон">
        <?php if (isset($errors['name'])): ?>
            <div class="invalid-feedback">
                <?= implode('<br>', $errors['name']) ?>
            </div>
        <?php endif; ?>
    </div>

    <div class="form-group">
        <label for="template-text">Текст шаблона *</label>
        <textarea class="form-control <?= isset($errors['template_text']) ? 'is-invalid' : '' ?>" 
                  id="template-text" name="template_text" rows="6" required 
                  placeholder="Hurmatli mijoz!

%w

Batafsil ma'lumot: https://carpet.idarmon.uz

SAG GILAMLAR BAZASI"><?= Html::encode($template_text) ?></textarea>
        <small class="form-text text-muted">
            Обязательно включите %w - это место для вставки динамического текста
        </small>
        <?php if (isset($errors['template_text'])): ?>
            <div class="invalid-feedback">
                <?= implode('<br>', $errors['template_text']) ?>
            </div>
        <?php endif; ?>
    </div>

    <?php if (isset($errors['general'])): ?>
        <div class="alert alert-danger">
            <?= implode('<br>', $errors['general']) ?>
        </div>
    <?php endif; ?>
</form>

<script>
$(document).ready(function() {
    // Проверка наличия %w при вводе
    $('#template-text').on('input', function() {
        var text = $(this).val();
        var hasPlaceholder = text.indexOf('%w') !== -1;
        
        if (text.length > 0 && !hasPlaceholder) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">Шаблон должен содержать %w для вставки динамического текста</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });
});
</script>
