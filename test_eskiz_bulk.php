<?php

// Простой тест для проверки API Eskiz массовой отправки

// Конфигурация из params.php
$eskizLogin = '<EMAIL>';
$eskizPassword = 'Tt03tBBJvyeWwg59E8mmeN51U8LUK1Q40iXNiDtq';
$smsNickName = 'CARPET';

// Функция для логина в API Eskiz
function eskizLogin($email, $password)
{
    $curl = curl_init();

    curl_setopt_array($curl, [
        CURLOPT_URL => 'https://notify.eskiz.uz/api/auth/login',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_POSTFIELDS => [
            'email' => $email,
            'password' => $password,
        ],
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

    if (curl_errno($curl)) {
        curl_close($curl);
        throw new Exception('cURL error: ' . curl_error($curl));
    }

    curl_close($curl);

    $responseData = json_decode($response, true);

    if ($httpCode !== 200 || !isset($responseData['data']['token'])) {
        throw new Exception('Login failed: ' . $response);
    }

    return $responseData['data']['token'];
}

// Функция для отправки batch SMS
function sendBatchSms($token, $messages, $from)
{
    $curl = curl_init();

    curl_setopt_array($curl, [
        CURLOPT_URL => 'https://notify.eskiz.uz/api/message/sms/send-batch',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 60,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_POSTFIELDS => json_encode([
            'messages' => $messages,
            'from' => $from,
        ]),
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json',
            'Accept: application/json',
        ],
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

    if (curl_errno($curl)) {
        curl_close($curl);
        throw new Exception('cURL error: ' . curl_error($curl));
    }

    curl_close($curl);

    echo "HTTP Code: $httpCode\n";
    echo "Response: $response\n";

    return json_decode($response, true);
}

// Функция для отправки одиночного SMS (для сравнения)
function sendSingleSms($token, $phone, $message, $from)
{
    $curl = curl_init();

    curl_setopt_array($curl, [
        CURLOPT_URL => 'https://notify.eskiz.uz/api/message/sms/send',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_POSTFIELDS => [
            'mobile_phone' => $phone,
            'message' => $message,
            'from' => $from,
        ],
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $token,
            'Content-Type: multipart/form-data',
        ],
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

    if (curl_errno($curl)) {
        curl_close($curl);
        throw new Exception('cURL error: ' . curl_error($curl));
    }

    curl_close($curl);

    echo "Single SMS HTTP Code: $httpCode\n";
    echo "Single SMS Response: $response\n";

    return json_decode($response, true);
}

// Функция для получения шаблонов
function getTemplates($token)
{
    $curl = curl_init();

    curl_setopt_array($curl, [
        CURLOPT_URL => 'https://notify.eskiz.uz/api/user/templates',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $token,
        ],
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

    if (curl_errno($curl)) {
        curl_close($curl);
        throw new Exception('cURL error: ' . curl_error($curl));
    }

    curl_close($curl);

    echo "Templates HTTP Code: $httpCode\n";
    echo "Templates Response: $response\n";

    return json_decode($response, true);
}

try {
    echo "=== Тест API Eskiz для массовой отправки SMS ===\n\n";

    // 1. Логинимся
    echo "1. Логинимся в API Eskiz...\n";
    $token = eskizLogin($eskizLogin, $eskizPassword);
    echo "Токен получен: " . substr($token, 0, 20) . "...\n\n";

    // 2. Получаем шаблоны
    echo "2. Получаем доступные шаблоны...\n";
    $templatesResult = getTemplates($token);
    echo "\n";

    // Ищем подтвержденный шаблон
    $approvedTemplate = null;
    if (isset($templatesResult['result']) && is_array($templatesResult['result'])) {
        foreach ($templatesResult['result'] as $template) {
            if (isset($template['status']) && $template['status'] === 'reklama') {
                $approvedTemplate = $template['template'];
                echo "Найден подтвержденный шаблон: " . $approvedTemplate . "\n\n";
                break;
            }
        }
    }

    if (!$approvedTemplate) {
        echo "Подтвержденный шаблон не найден. Используем простое сообщение.\n\n";
        $approvedTemplate = 'Test message: %w';
    }

    // Подготавливаем сообщение с шаблоном
    $testMessage = str_replace('%w', 'Тестовое сообщение', $approvedTemplate);

    // 3. Тестируем одиночную отправку с шаблоном
    echo "3. Тестируем одиночную отправку SMS с шаблоном...\n";
    echo "Сообщение: $testMessage\n";
    // Закомментируем реальную отправку для безопасности
    // $singleResult = sendSingleSms($token, '998901234567', $testMessage, $smsNickName);
    echo "Одиночная отправка пропущена (закомментирована для безопасности)\n\n";

    // 4. Тестируем массовую отправку с шаблоном
    echo "4. Тестируем массовую отправку SMS с шаблоном...\n";
    $messages = [
        [
            'user_sms_id' => '1',
            'to' => '998901234567',
            'text' => $testMessage,
        ],
        [
            'user_sms_id' => '2',
            'to' => '998901234568',
            'text' => $testMessage,
        ]
    ];

    echo "Сообщения для отправки:\n";
    foreach ($messages as $msg) {
        echo "- ID: {$msg['user_sms_id']}, Phone: {$msg['to']}, Text: {$msg['text']}\n";
    }
    echo "\n";

    // Закомментируем реальную отправку для безопасности
    // $batchResult = sendBatchSms($token, $messages, $smsNickName);
    echo "Массовая отправка пропущена (закомментирована для безопасности)\n\n";

    echo "=== Тест завершен ===\n";
} catch (Exception $e) {
    echo "Ошибка: " . $e->getMessage() . "\n";
}
