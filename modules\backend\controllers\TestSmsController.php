<?php

namespace app\modules\backend\controllers;

use app\common\models\Clients;
use app\common\services\EskizBulkSmsService;
use Yii;
use yii\filters\AccessControl;
use yii\web\Controller;
use yii\web\Response;

/**
 * Временный контроллер для тестирования массовой отправки SMS
 */
class TestSmsController extends Controller
{
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'rules' => [
                    [
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
        ];
    }

    /**
     * Страница тестирования
     */
    public function actionIndex()
    {
        // Получаем клиентов
        $clients = Clients::find()
            ->where(['deleted_at' => null])
            ->andWhere(['!=', 'phone_number', ''])
            ->andWhere(['!=', 'phone_number', null])
            ->andWhere(['status' => 1])
            ->limit(10)
            ->all();

        // Проверяем доступность API
        $eskizBulkSmsService = new EskizBulkSmsService();
        $serviceAvailable = $eskizBulkSmsService->isServiceAvailable();

        return $this->render('index', [
            'clients' => $clients,
            'serviceAvailable' => $serviceAvailable,
        ]);
    }

    /**
     * Тест массовой отправки
     */
    public function actionTestBulkSms()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $message = Yii::$app->request->post('message');
        $useTemplate = (bool)Yii::$app->request->post('use_template', true);

        if (empty($message)) {
            return ['success' => false, 'message' => 'Введите текст сообщения'];
        }

        try {
            // Получаем клиентов
            $clients = Clients::find()
                ->where(['deleted_at' => null])
                ->andWhere(['!=', 'phone_number', ''])
                ->andWhere(['!=', 'phone_number', null])
                ->andWhere(['status' => 1])
                ->limit(10)
                ->all();

            if (empty($clients)) {
                return ['success' => false, 'message' => 'Клиенты не найдены'];
            }

            // Формируем список получателей
            $eskizBulkSmsService = new EskizBulkSmsService();
            $recipients = [];

            foreach ($clients as $client) {
                $phone = $eskizBulkSmsService->formatPhone($client->phone_number);

                if ($eskizBulkSmsService->validatePhone($phone)) {
                    $recipients[] = [
                        'client_id' => $client->id,
                        'phone' => $phone,
                        'name' => $client->full_name ?: 'Клиент',
                    ];
                }
            }

            if (empty($recipients)) {
                return ['success' => false, 'message' => 'Нет валидных получателей'];
            }

            // Отправляем SMS
            $result = $eskizBulkSmsService->sendBulkSms($message, $recipients, $useTemplate);

            return [
                'success' => true,
                'message' => 'SMS отправлены успешно',
                'sent_count' => $result['sent_count'] ?? count($recipients),
                'recipients_count' => count($recipients),
                'recipients' => array_map(function($r) {
                    return $r['name'] . ' (' . $r['phone'] . ')';
                }, $recipients),
                'api_result' => $result['result'] ?? null,
            ];

        } catch (\Exception $e) {
            Yii::error('Ошибка тестовой отправки SMS: ' . $e->getMessage(), 'test-sms');
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Проверка API Eskiz
     */
    public function actionCheckApi()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        try {
            $eskizBulkSmsService = new EskizBulkSmsService();
            
            // Проверяем доступность
            $serviceAvailable = $eskizBulkSmsService->isServiceAvailable();
            
            if (!$serviceAvailable) {
                return ['success' => false, 'message' => 'API Eskiz недоступен'];
            }

            // Получаем токен для проверки авторизации
            $token = Yii::$app->cache->getOrSet('eskiz-bearer-token', function () use ($eskizBulkSmsService) {
                $reflection = new \ReflectionClass($eskizBulkSmsService);
                $method = $reflection->getMethod('login');
                $method->setAccessible(true);
                $response = $method->invoke($eskizBulkSmsService);
                return $response['data']['token'];
            });

            // Получаем шаблоны
            $reflection = new \ReflectionClass($eskizBulkSmsService);
            $method = $reflection->getMethod('getApprovedTemplate');
            $method->setAccessible(true);
            $template = $method->invoke($eskizBulkSmsService, $token);

            return [
                'success' => true,
                'message' => 'API Eskiz доступен',
                'token_preview' => substr($token, 0, 20) . '...',
                'approved_template' => $template,
            ];

        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
}
