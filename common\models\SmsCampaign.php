<?php

namespace app\common\models;

use Yii;
use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;
use yii\behaviors\BlameableBehavior;

/**
 * Модель SMS кампании
 *
 * @property int $id
 * @property string $name Название кампании
 * @property string $message Текст сообщения
 * @property bool $template_used Использовался ли шаблон
 * @property string|null $template_text Текст шаблона
 * @property int $total_recipients Общее количество получателей
 * @property int $sent_count Количество отправленных SMS
 * @property int $delivered_count Количество доставленных SMS
 * @property int $failed_count Количество неудачных отправок
 * @property int $status Статус кампании
 * @property string|null $eskiz_campaign_id ID кампании в Eskiz
 * @property array|null $filters_used Использованные фильтры
 * @property int|null $created_by Кто создал кампанию
 * @property string $created_at
 * @property string $updated_at
 * @property string|null $started_at Время начала отправки
 * @property string|null $completed_at Время завершения
 *
 * @property SmsCampaignRecipient[] $recipients
 * @property User $createdBy
 */
class SmsCampaign extends ActiveRecord
{
    // Статусы кампании
    const STATUS_CREATED = 0;      // Создана
    const STATUS_PROCESSING = 1;   // Обрабатывается
    const STATUS_COMPLETED = 2;    // Завершена
    const STATUS_CANCELLED = 3;    // Отменена
    const STATUS_ERROR = 4;        // Ошибка

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%sms_campaigns}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => function() {
                    return date('Y-m-d H:i:s');
                },
            ],
            [
                'class' => BlameableBehavior::class,
                'createdByAttribute' => 'created_by',
                'updatedByAttribute' => false,
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name', 'message'], 'required'],
            [['message', 'template_text'], 'string'],
            [['template_used'], 'boolean'],
            [['total_recipients', 'sent_count', 'delivered_count', 'failed_count', 'status', 'created_by'], 'integer'],
            [['filters_used'], 'safe'],
            [['created_at', 'updated_at', 'started_at', 'completed_at'], 'safe'],
            [['name'], 'string', 'max' => 255],
            [['eskiz_campaign_id'], 'string', 'max' => 100],
            [['status'], 'in', 'range' => [self::STATUS_CREATED, self::STATUS_PROCESSING, self::STATUS_COMPLETED, self::STATUS_CANCELLED, self::STATUS_ERROR]],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Название кампании',
            'message' => 'Текст сообщения',
            'template_used' => 'Использовался шаблон',
            'template_text' => 'Текст шаблона',
            'total_recipients' => 'Всего получателей',
            'sent_count' => 'Отправлено',
            'delivered_count' => 'Доставлено',
            'failed_count' => 'Ошибок',
            'status' => 'Статус',
            'eskiz_campaign_id' => 'ID в Eskiz',
            'filters_used' => 'Фильтры',
            'created_by' => 'Создал',
            'created_at' => 'Создана',
            'updated_at' => 'Обновлена',
            'started_at' => 'Начата',
            'completed_at' => 'Завершена',
        ];
    }

    /**
     * Получить получателей кампании
     */
    public function getRecipients()
    {
        return $this->hasMany(SmsCampaignRecipient::class, ['campaign_id' => 'id']);
    }

    /**
     * Получить создателя кампании
     */
    public function getCreatedBy()
    {
        return $this->hasOne(User::class, ['id' => 'created_by']);
    }

    /**
     * Получить статусы кампаний
     */
    public static function getStatusLabels()
    {
        return [
            self::STATUS_CREATED => 'Создана',
            self::STATUS_PROCESSING => 'Обрабатывается',
            self::STATUS_COMPLETED => 'Завершена',
            self::STATUS_CANCELLED => 'Отменена',
            self::STATUS_ERROR => 'Ошибка',
        ];
    }

    /**
     * Получить метку статуса
     */
    public function getStatusLabel()
    {
        $labels = self::getStatusLabels();
        return $labels[$this->status] ?? 'Неизвестно';
    }

    /**
     * Получить CSS класс для статуса
     */
    public function getStatusClass()
    {
        $classes = [
            self::STATUS_CREATED => 'secondary',
            self::STATUS_PROCESSING => 'warning',
            self::STATUS_COMPLETED => 'success',
            self::STATUS_CANCELLED => 'secondary',
            self::STATUS_ERROR => 'danger',
        ];
        return $classes[$this->status] ?? 'light';
    }

    /**
     * Получить процент успешности
     */
    public function getSuccessRate()
    {
        if ($this->total_recipients == 0) {
            return 0;
        }
        return round(($this->sent_count / $this->total_recipients) * 100, 1);
    }

    /**
     * Получить процент доставки
     */
    public function getDeliveryRate()
    {
        if ($this->sent_count == 0) {
            return 0;
        }
        return round(($this->delivered_count / $this->sent_count) * 100, 1);
    }

    /**
     * Обновить статистику кампании
     */
    public function updateStats()
    {
        $stats = SmsCampaignRecipient::find()
            ->where(['campaign_id' => $this->id])
            ->select([
                'COUNT(*) as total',
                'SUM(CASE WHEN status >= 1 THEN 1 ELSE 0 END) as sent',
                'SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as delivered',
                'SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as failed',
            ])
            ->asArray()
            ->one();

        if ($stats) {
            $this->total_recipients = (int)$stats['total'];
            $this->sent_count = (int)$stats['sent'];
            $this->delivered_count = (int)$stats['delivered'];
            $this->failed_count = (int)$stats['failed'];
            
            // Обновляем статус кампании
            if ($this->failed_count > 0 && $this->sent_count == 0) {
                $this->status = self::STATUS_ERROR;
            } elseif ($this->sent_count == $this->total_recipients) {
                $this->status = self::STATUS_COMPLETED;
                if (!$this->completed_at) {
                    $this->completed_at = date('Y-m-d H:i:s');
                }
            } elseif ($this->sent_count > 0) {
                $this->status = self::STATUS_PROCESSING;
                if (!$this->started_at) {
                    $this->started_at = date('Y-m-d H:i:s');
                }
            }
            
            $this->save(false);
        }
    }

    /**
     * Получить последние кампании
     */
    public static function getRecentCampaigns($limit = 10)
    {
        return self::find()
            ->orderBy(['created_at' => SORT_DESC])
            ->limit($limit)
            ->all();
    }
}
