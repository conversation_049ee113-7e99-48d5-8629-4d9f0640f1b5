<?php

use yii\helpers\Html;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $name string */
/* @var $message string */
/* @var $client_ids array */
/* @var $use_template bool */
/* @var $template_id string */
/* @var $errors array */

?>

<form id="campaign-form">
    <div class="form-group">
        <label for="campaign-name">Название кампании *</label>
        <input type="text" class="form-control <?= isset($errors['name']) ? 'is-invalid' : '' ?>"
            id="campaign-name" name="name" value="<?= Html::encode($name) ?>" required>
        <?php if (isset($errors['name'])): ?>
            <div class="invalid-feedback">
                <?= implode('<br>', $errors['name']) ?>
            </div>
        <?php endif; ?>
    </div>

    <div class="form-group">
        <label for="campaign-message">Текст сообщения *</label>
        <textarea class="form-control <?= isset($errors['message']) ? 'is-invalid' : '' ?>"
            id="campaign-message" name="message" rows="4" maxlength="1000" required
            placeholder="Например: 50% скидка на все ковры до конца месяца!"><?= Html::encode($message) ?></textarea>
        <small class="form-text text-muted">Максимум 1000 символов. Этот текст будет вставлен в шаблон.</small>
        <?php if (isset($errors['message'])): ?>
            <div class="invalid-feedback">
                <?= implode('<br>', $errors['message']) ?>
            </div>
        <?php endif; ?>
    </div>

    <div class="form-group">
        <div class="custom-control custom-checkbox">
            <input type="checkbox" class="custom-control-input" id="campaign-use-template"
                name="use_template" value="1" <?= $use_template ? 'checked' : '' ?>>
            <label class="custom-control-label" for="campaign-use-template">
                <strong>Использовать шаблон</strong> (рекомендуется для высокой доставляемости)
            </label>
        </div>
    </div>

    <div class="form-group" id="template-selection" style="<?= $use_template ? '' : 'display: none;' ?>">
        <label for="campaign-template">Выберите шаблон</label>
        <select class="form-control select2" id="campaign-template" name="template_id">
            <option value="">Загрузка шаблонов...</option>
        </select>
    </div>

    <div class="alert alert-success">
        <i class="fas fa-users"></i> <strong>Получатели:</strong> SMS будет отправлено всем активным клиентам с номерами телефонов
    </div>

    <?php if (isset($errors['general'])): ?>
        <div class="alert alert-danger">
            <?= implode('<br>', $errors['general']) ?>
        </div>
    <?php endif; ?>
</form>

<script>
    $(document).ready(function() {


        // Загружаем доступные шаблоны
        function loadTemplates() {
            $.ajax({
                url: '<?= Url::to(['/backend/bulk-sms/get-templates']) ?>',
                type: 'GET',
                dataType: 'json',
                success: function(templates) {
                    var select = $('#campaign-template');
                    select.empty();
                    select.append('<option value="">Без шаблона</option>');

                    $.each(templates, function(index, template) {
                        var label = template.name;
                        if (template.is_default) {
                            label += ' (по умолчанию)';
                        }
                        var selected = '<?= $template_id ?>' == template.id ? 'selected' : '';
                        select.append('<option value="' + template.id + '" ' + selected + '>' + label + '</option>');
                    });

                    // Выбираем шаблон по умолчанию если не выбран
                    if (!'<?= $template_id ?>') {
                        var defaultTemplate = templates.find(t => t.is_default);
                        if (defaultTemplate) {
                            select.val(defaultTemplate.id);
                        }
                    }
                },
                error: function() {
                    $('#campaign-template').html('<option value="">Ошибка загрузки шаблонов</option>');
                }
            });
        }

        // Показать/скрыть выбор шаблона
        $('#campaign-use-template').on('change', function() {
            if ($(this).is(':checked')) {
                $('#template-selection').show();
                loadTemplates();
            } else {
                $('#template-selection').hide();
                $('#template-preview').hide();
            }
        });

        // Предварительный просмотр шаблона
        $('#preview-template-btn').on('click', function() {
            var templateId = $('#campaign-template').val();
            var message = $('#campaign-message').val();

            if (!templateId) {
                iziToast.warning({
                    title: 'Внимание',
                    message: 'Выберите шаблон для предварительного просмотра',
                    position: 'topRight'
                });
                return;
            }

            if (!message) {
                iziToast.warning({
                    title: 'Внимание',
                    message: 'Введите текст сообщения для предварительного просмотра',
                    position: 'topRight'
                });
                return;
            }

            $.ajax({
                url: '<?= Url::to(['/backend/bulk-sms/preview-template']) ?>',
                type: 'POST',
                data: {
                    template_id: templateId,
                    message: message
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        $('#preview-content').html('<pre>' + response.preview + '</pre>');
                        $('#template-preview').show();
                    } else {
                        iziToast.error({
                            title: 'Ошибка',
                            message: response.message,
                            position: 'topRight'
                        });
                    }
                },
                error: function() {
                    iziToast.error({
                        title: 'Ошибка',
                        message: 'Не удалось загрузить предварительный просмотр',
                        position: 'topRight'
                    });
                }
            });
        });

        // Загружаем шаблоны при загрузке формы
        if ($('#campaign-use-template').is(':checked')) {
            loadTemplates();
        }
    });
</script>