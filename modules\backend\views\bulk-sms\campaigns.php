<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;
use yii\widgets\Pjax;

/* @var $this yii\web\View */
/* @var $campaigns app\common\models\SmsCampaign[] */
/* @var $totalStats array */
/* @var $statusStats array */
/* @var $dailyStats array */

$this->title = 'Статистика SMS кампаний';
$this->params['breadcrumbs'][] = ['label' => 'SMS рассылка', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<style>
    .modal-dialog {
        max-width: 600px;
    }

    .search-form {
        margin-bottom: 20px;
    }

    .pagination {
        justify-content: center;
        margin-top: 20px;
    }
</style>

<div class="card-body">

    <?php Pjax::begin(['id' => 'campaigns-pjax']); ?>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3 class="card-title mb-0">
            <?= Html::encode($this->title) ?>
        </h3>
        <div class="btn-group">
            <button type="button" class="btn btn-success create-campaign">
                <i class="fas fa-plus"></i> Создать отправление
            </button>
            <button type="button" class="btn btn-warning create-template">
                <i class="fas fa-file-alt"></i> Создать шаблон
            </button>
        </div>
    </div>

    <?php if (!empty($campaigns)): ?>
        <div class="">
            <table class="table table-bordered table-striped compact" id="campaigns">
                <thead>
                    <th>ID</th>
                    <th>Название</th>
                    <th>Сообщение</th>
                    <th>Шаблон</th>
                    <th>Получателей</th>
                    <th>Отправлено</th>
                    <th>Доставлено</th>
                    <th>Ошибок</th>
                    <th>Успешность</th>
                    <th>Статус</th>
                    <th>Создана</th>
                    <th>Действия</th>
                </thead>
                <tbody>
                    <?php foreach ($campaigns as $campaign): ?>
                        <tr>
                            <td><?= $campaign->id ?></td>
                            <td><?= Html::encode($campaign->name) ?></td>
                            <td>
                                <span class="text-muted" title="<?= Html::encode($campaign->message) ?>">
                                    <?= Html::encode(mb_substr($campaign->message, 0, 50)) ?>
                                    <?= mb_strlen($campaign->message) > 50 ? '...' : '' ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($campaign->template_used): ?>
                                    <span class="badge badge-success">Да</span>
                                <?php else: ?>
                                    <span class="badge badge-secondary">Нет</span>
                                <?php endif; ?>
                            </td>
                            <td><?= number_format($campaign->total_recipients) ?></td>
                            <td>
                                <span class="badge badge-info"><?= number_format($campaign->sent_count) ?></span>
                            </td>
                            <td>
                                <span class="badge badge-success"><?= number_format($campaign->delivered_count) ?></span>
                            </td>
                            <td>
                                <?php if ($campaign->failed_count > 0): ?>
                                    <span class="badge badge-danger"><?= number_format($campaign->failed_count) ?></span>
                                <?php else: ?>
                                    <span class="text-muted">0</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge badge-<?= $campaign->getSuccessRate() >= 90 ? 'success' : ($campaign->getSuccessRate() >= 70 ? 'warning' : 'danger') ?>">
                                    <?= $campaign->getSuccessRate() ?>%
                                </span>
                            </td>
                            <td>
                                <span class="badge badge-<?= $campaign->getStatusClass() ?>">
                                    <?= $campaign->getStatusLabel() ?>
                                </span>
                            </td>
                            <td><?= date('d.m.Y H:i', strtotime($campaign->created_at)) ?></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-primary dropdown-toggle" type="button" data-toggle="dropdown">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item campaign-details" href="#" data-id="<?= $campaign->id ?>">
                                            <i class="fas fa-eye"></i> Детали
                                        </a>
                                        <a class="dropdown-item campaign-edit" href="#" data-id="<?= $campaign->id ?>">
                                            <i class="fas fa-edit"></i> Редактировать
                                        </a>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <p><?= Yii::t("app", "no_data_available"); ?></p>
        <?php endif; ?>

        <?php Pjax::end(); ?>
        </div>

        <div id="one" data-text="<?= Yii::t("app", "create_campaign") ?>"></div>
        <div id="two" data-text="<?= Yii::t("app", "create_template") ?>"></div>
        <div id="three" data-text="<?= Yii::t("app", "campaign_details") ?>"></div>





        <?php
        $js = <<<JS
(function($) {
    // Объявляем переменные только один раз
    const createCampaign = $('#one').attr('data-text');
    const createTemplate = $('#two').attr('data-text');
    const campaignDetails = $('#three').attr('data-text');

    // Инициализация выпадающего меню
    function initializeDropdown() {
        $('.dropdown-toggle').each(function() {
            const button = $(this);
            if (!button.data('handler-attached')) {
                button.on('click.dropdown', function(e) {
                    e.preventDefault();
                    const dropdownMenu = button.next('.dropdown-menu');
                    $('.dropdown-menu').not(dropdownMenu).hide();
                    dropdownMenu.toggle();
                });
                button.data('handler-attached', true);
            }
        });

        $(document).on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').hide();
            }
        });
    }

    // Инициализация Select2
    function initializeSelect2() {
        if ($('.select2').length > 0) {
            $('.select2').select2({
                width: '100%'
            });
        }
    }

    // Инициализация создания кампании
    function initializeCampaignCreate() {
        $('.create-campaign').each(function() {
            const button = $(this);
            if (!button.data('handler-attached')) {
                button.on('click.campaign-create', function() {
                    $.ajax({
                        url: '/backend/bulk-sms/create-campaign',
                        dataType: 'json',
                        type: 'GET',
                        success: function(response) {
                            $('#ideal-mini-modal .modal-title').html(createCampaign);
                            $('#ideal-mini-modal .modal-body').html(response.content);
                            $('#ideal-mini-modal .mini-button').addClass("campaign-create-button");
                            $('#ideal-mini-modal').modal('show');
                            initializeSelect2();
                        },
                        error: function(xhr, textStatus, errorThrown) {
                            console.error('AJAX Error:', xhr.statusText, errorThrown);
                        }
                    });
                });
                button.data('handler-attached', true);
            }
        });
    }

    // Инициализация создания шаблона
    function initializeTemplateCreate() {
        $('.create-template').each(function() {
            const button = $(this);
            if (!button.data('handler-attached')) {
                button.on('click.template-create', function() {
                    $.ajax({
                        url: '/backend/bulk-sms/create-template',
                        dataType: 'json',
                        type: 'GET',
                        success: function(response) {
                            $('#ideal-mini-modal .modal-title').html(createTemplate);
                            $('#ideal-mini-modal .modal-body').html(response.content);
                            $('#ideal-mini-modal .mini-button').addClass("template-create-button");
                            $('#ideal-mini-modal').modal('show');
                        },
                        error: function(xhr, textStatus, errorThrown) {
                            console.error('AJAX Error:', xhr.statusText, errorThrown);
                        }
                    });
                });
                button.data('handler-attached', true);
            }
        });
    }

    // Инициализация просмотра деталей кампании
    function initializeCampaignDetails() {
        $('.campaign-details').each(function() {
            const button = $(this);
            if (!button.data('handler-attached')) {
                button.on('click.campaign-details', function(e) {
                    e.preventDefault();
                    const campaignId = $(this).data('id');
                    $.ajax({
                        url: '/backend/bulk-sms/campaign-details',
                        type: 'GET',
                        data: { id: campaignId },
                        success: function(response) {
                            if (response.status === 'success') {
                                const modal = $('#ideal-mini-modal');
                                modal.find('.modal-title').html(campaignDetails);
                                modal.find('.modal-body').html(response.content);
                                modal.find('.mini-button').hide();
                                modal.modal('show');
                            } else {
                                iziToast.error({
                                    title: 'Ошибка',
                                    message: response.message,
                                    position: 'topRight'
                                });
                            }
                        },
                        error: function(xhr, textStatus, errorThrown) {
                            console.error('AJAX Error:', xhr.statusText, errorThrown);
                        }
                    });
                });
                button.data('handler-attached', true);
            }
        });
    }

    // Обработка создания кампании
    $(document).on('click', '.campaign-create-button', function() {
        const form = $('#campaign-form');
        $.ajax({
            url: '/backend/bulk-sms/create-campaign',
            dataType: 'json',
            type: 'post',
            data: form.serialize(),
            success: function(response) {
                if (response.status == 'error') {
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    initializeSelect2();
                } else if (response.status == 'success') {
                    $("#ideal-mini-modal").modal('toggle');
                    $.pjax.reload({container: "#campaigns-pjax"});
                    iziToast.success({
                        title: 'Успех',
                        message: response.message,
                        position: 'topRight'
                    });
                }
            }
        });
    });

    // Обработка создания шаблона
    $(document).on('click', '.template-create-button', function() {
        const form = $('#template-form');
        $.ajax({
            url: '/backend/bulk-sms/create-template',
            dataType: 'json',
            type: 'post',
            data: form.serialize(),
            success: function(response) {
                if (response.status == 'error') {
                    $('#ideal-mini-modal .modal-body').html(response.content);
                } else if (response.status == 'success') {
                    $("#ideal-mini-modal").modal('toggle');
                    $.pjax.reload({container: "#campaigns-pjax"});
                    iziToast.success({
                        title: 'Успех',
                        message: response.message,
                        position: 'topRight'
                    });
                }
            }
        });
    });

    // Инициализация всех функций
    function initializeAll() {
        initializeDropdown();
        initializeSelect2();
        initializeCampaignCreate();
        initializeTemplateCreate();
        initializeCampaignDetails();
    }

    // Инициализация при загрузке страницы
    $(document).ready(function() {
        if (!window.initialized) {
            initializeAll();
            window.initialized = true;
        }
    });

    // Инициализация при PJAX-загрузке
    $(document).on('pjax:complete', function() {
        initializeAll();
    });
})(jQuery);
JS;
        $this->registerJs($js, View::POS_END);
        ?>