<?php

use yii\helpers\Html;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $campaigns app\common\models\SmsCampaign[] */
/* @var $totalStats array */
/* @var $statusStats array */
/* @var $dailyStats array */

$this->title = 'Статистика SMS кампаний';
$this->params['breadcrumbs'][] = ['label' => 'SMS рассылка', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="card-body">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3 class="card-title mb-0">
                    <i class="fas fa-chart-bar"></i>
                    <?= Html::encode($this->title) ?>
                </h3>
                <div class="btn-group">
                    <button type="button" class="btn btn-success" data-toggle="modal" data-target="#createCampaignModal">
                        <i class="fas fa-plus"></i> Создать отправление
                    </button>
                    <button type="button" class="btn btn-warning" data-toggle="modal" data-target="#createTemplateModal">
                        <i class="fas fa-file-alt"></i> Создать шаблон
                    </button>
                    <?= Html::a('<i class="fas fa-cog"></i> Управление шаблонами', ['templates'], ['class' => 'btn btn-info']) ?>
                    <?= Html::a('<i class="fas fa-arrow-left"></i> Назад к рассылке', ['index'], ['class' => 'btn btn-secondary']) ?>
                </div>
            </div>

            <!-- Общая статистика -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="info-box">
                        <span class="info-box-icon bg-info"><i class="fas fa-bullhorn"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Всего кампаний</span>
                            <span class="info-box-number"><?= $totalStats['total_campaigns'] ?? 0 ?></span>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="info-box">
                        <span class="info-box-icon bg-primary"><i class="fas fa-users"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Получателей</span>
                            <span class="info-box-number"><?= number_format($totalStats['total_recipients'] ?? 0) ?></span>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="info-box">
                        <span class="info-box-icon bg-success"><i class="fas fa-paper-plane"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Отправлено</span>
                            <span class="info-box-number"><?= number_format($totalStats['total_sent'] ?? 0) ?></span>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="info-box">
                        <span class="info-box-icon bg-success"><i class="fas fa-check-double"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Доставлено</span>
                            <span class="info-box-number"><?= number_format($totalStats['total_delivered'] ?? 0) ?></span>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="info-box">
                        <span class="info-box-icon bg-danger"><i class="fas fa-times"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Ошибок</span>
                            <span class="info-box-number"><?= number_format($totalStats['total_failed'] ?? 0) ?></span>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="info-box">
                        <span class="info-box-icon bg-warning"><i class="fas fa-percentage"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Успешность</span>
                            <span class="info-box-number">
                                <?php
                                $successRate = 0;
                                if (($totalStats['total_recipients'] ?? 0) > 0) {
                                    $successRate = round((($totalStats['total_sent'] ?? 0) / $totalStats['total_recipients']) * 100, 1);
                                }
                                echo $successRate . '%';
                                ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Список кампаний -->

            <div class="card-body p-0">
                <?php if (!empty($campaigns)): ?>
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Название</th>
                                <th>Сообщение</th>
                                <th>Шаблон</th>
                                <th>Получателей</th>
                                <th>Отправлено</th>
                                <th>Доставлено</th>
                                <th>Ошибок</th>
                                <th>Успешность</th>
                                <th>Статус</th>
                                <th>Создана</th>
                                <th>Действия</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($campaigns as $campaign): ?>
                                <tr>
                                    <td><?= $campaign->id ?></td>
                                    <td><?= Html::encode($campaign->name) ?></td>
                                    <td>
                                        <span class="text-muted" title="<?= Html::encode($campaign->message) ?>">
                                            <?= Html::encode(mb_substr($campaign->message, 0, 50)) ?>
                                            <?= mb_strlen($campaign->message) > 50 ? '...' : '' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($campaign->template_used): ?>
                                            <span class="badge badge-success">Да</span>
                                        <?php else: ?>
                                            <span class="badge badge-secondary">Нет</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= number_format($campaign->total_recipients) ?></td>
                                    <td>
                                        <span class="badge badge-info"><?= number_format($campaign->sent_count) ?></span>
                                    </td>
                                    <td>
                                        <span class="badge badge-success"><?= number_format($campaign->delivered_count) ?></span>
                                    </td>
                                    <td>
                                        <?php if ($campaign->failed_count > 0): ?>
                                            <span class="badge badge-danger"><?= number_format($campaign->failed_count) ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">0</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?= $campaign->getSuccessRate() >= 90 ? 'success' : ($campaign->getSuccessRate() >= 70 ? 'warning' : 'danger') ?>">
                                            <?= $campaign->getSuccessRate() ?>%
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?= $campaign->getStatusClass() ?>">
                                            <?= $campaign->getStatusLabel() ?>
                                        </span>
                                    </td>
                                    <td><?= date('d.m.Y H:i', strtotime($campaign->created_at)) ?></td>
                                    <td>
                                        <?= Html::a('<i class="fas fa-eye"></i>', ['campaign-details', 'id' => $campaign->id], [
                                            'class' => 'btn btn-sm btn-info',
                                            'title' => 'Детали кампании'
                                        ]) ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <div>
                        <h6> <?= Yii::t('app', 'No data available.') ?></h6>
                    </div>
                <?php endif; ?>
            </div>



        </div>
    </div>
</div>

<!-- Модальное окно создания кампании -->
<div class="modal fade" id="createCampaignModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Создать SMS кампанию</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <form id="createCampaignForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="campaign-name">Название кампании *</label>
                        <input type="text" class="form-control" id="campaign-name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="campaign-message">Текст сообщения *</label>
                        <textarea class="form-control" id="campaign-message" name="message" rows="4" maxlength="1000" required placeholder="Например: 50% скидка на все ковры до конца месяца!"></textarea>
                        <small class="form-text text-muted">Максимум 1000 символов. Этот текст будет вставлен в шаблон.</small>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="campaign-use-template" name="use_template" value="1" checked>
                            <label class="custom-control-label" for="campaign-use-template">
                                <strong>Использовать шаблон</strong> (рекомендуется для высокой доставляемости)
                            </label>
                        </div>
                    </div>
                    <div class="form-group" id="template-selection">
                        <label for="campaign-template">Выберите шаблон</label>
                        <select class="form-control" id="campaign-template" name="template_id">
                            <option value="">Загрузка шаблонов...</option>
                        </select>
                        <small class="form-text text-muted">
                            <button type="button" class="btn btn-sm btn-link p-0" id="preview-template-btn">
                                <i class="fas fa-eye"></i> Предварительный просмотр
                            </button>
                        </small>
                        <div id="template-preview" class="mt-2" style="display: none;">
                            <div class="alert alert-info">
                                <strong>Предварительный просмотр:</strong>
                                <div id="preview-content"></div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="campaign-clients">Получатели *</label>
                        <select class="form-control" id="campaign-clients" name="client_ids[]" multiple required>
                        </select>
                        <small class="form-text text-muted">Начните вводить имя клиента для поиска</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Отмена</button>
                    <button type="submit" class="btn btn-success">Создать кампанию</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Модальное окно создания шаблона -->
<div class="modal fade" id="createTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Создать шаблон SMS</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <form id="createTemplateForm">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> Важно!</h5>
                        <p>Шаблон должен содержать <code>%w</code> - место для вставки динамического текста.</p>
                        <p><strong>Пример:</strong> "Hurmatli mijoz! %w Rahmat!"</p>
                    </div>
                    <div class="form-group">
                        <label for="template-name">Название шаблона *</label>
                        <input type="text" class="form-control" id="template-name" name="name" required placeholder="Например: Рекламный шаблон">
                    </div>
                    <div class="form-group">
                        <label for="template-text">Текст шаблона *</label>
                        <textarea class="form-control" id="template-text" name="template_text" rows="6" required placeholder="Hurmatli mijoz!

%w

Batafsil ma'lumot: https://carpet.idarmon.uz

SAG GILAMLAR BAZASI"></textarea>
                        <small class="form-text text-muted">
                            Обязательно включите %w - это место для вставки динамического текста
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Отмена</button>
                    <button type="submit" class="btn btn-warning">Создать шаблон</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Подключаем Select2 для поиска клиентов
$this->registerJsFile('https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js', ['depends' => [\yii\web\JqueryAsset::class]]);
$this->registerCssFile('https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css');

// Подключаем iziToast для уведомлений
$this->registerJsFile('https://cdn.jsdelivr.net/npm/izitoast@1.4.0/dist/js/iziToast.min.js');
$this->registerCssFile('https://cdn.jsdelivr.net/npm/izitoast@1.4.0/dist/css/iziToast.min.css');

$this->registerJs("
// Инициализация Select2 для поиска клиентов
$('#campaign-clients').select2({
    ajax: {
        url: '" . Url::to(['search-clients']) . "',
        dataType: 'json',
        delay: 250,
        data: function (params) {
            return {
                q: params.term,
                limit: 20
            };
        },
        processResults: function (data) {
            return {
                results: data.results
            };
        },
        cache: true
    },
    placeholder: 'Начните вводить имя клиента...',
    minimumInputLength: 2,
    width: '100%'
});

// Загружаем доступные шаблоны
function loadTemplates() {
    $.ajax({
        url: '" . Url::to(['get-templates']) . "',
        type: 'GET',
        dataType: 'json',
        success: function(templates) {
            var select = $('#campaign-template');
            select.empty();
            select.append('<option value=\"\">Без шаблона</option>');

            $.each(templates, function(index, template) {
                var label = template.name;
                if (template.is_default) {
                    label += ' (по умолчанию)';
                }
                select.append('<option value=\"' + template.id + '\">' + label + '</option>');
            });

            // Выбираем шаблон по умолчанию
            var defaultTemplate = templates.find(t => t.is_default);
            if (defaultTemplate) {
                select.val(defaultTemplate.id);
            }
        },
        error: function() {
            $('#campaign-template').html('<option value=\"\">Ошибка загрузки шаблонов</option>');
        }
    });
}

// Показать/скрыть выбор шаблона
$('#campaign-use-template').on('change', function() {
    if ($(this).is(':checked')) {
        $('#template-selection').show();
        loadTemplates();
    } else {
        $('#template-selection').hide();
        $('#template-preview').hide();
    }
});

// Предварительный просмотр шаблона
$('#preview-template-btn').on('click', function() {
    var templateId = $('#campaign-template').val();
    var message = $('#campaign-message').val();

    if (!templateId) {
        iziToast.warning({
            title: 'Внимание',
            message: 'Выберите шаблон для предварительного просмотра'
        });
        return;
    }

    if (!message) {
        iziToast.warning({
            title: 'Внимание',
            message: 'Введите текст сообщения для предварительного просмотра'
        });
        return;
    }

    $.ajax({
        url: '" . Url::to(['preview-template']) . "',
        type: 'POST',
        data: {
            template_id: templateId,
            message: message
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $('#preview-content').html('<pre>' + response.preview + '</pre>');
                $('#template-preview').show();
            } else {
                iziToast.error({
                    title: 'Ошибка',
                    message: response.message
                });
            }
        },
        error: function() {
            iziToast.error({
                title: 'Ошибка',
                message: 'Не удалось загрузить предварительный просмотр'
            });
        }
    });
});

// Обработка формы создания кампании
$('#createCampaignForm').on('submit', function(e) {
    e.preventDefault();

    var formData = $(this).serialize();
    var submitBtn = $(this).find('button[type=submit]');

    submitBtn.prop('disabled', true).html('<i class=\"fas fa-spinner fa-spin\"></i> Создание...');

    $.ajax({
        url: '" . Url::to(['create-campaign']) . "',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                iziToast.success({
                    title: 'Успех',
                    message: response.message
                });
                $('#createCampaignModal').modal('hide');
                location.reload();
            } else {
                iziToast.error({
                    title: 'Ошибка',
                    message: response.message
                });
            }
        },
        error: function() {
            iziToast.error({
                title: 'Ошибка',
                message: 'Произошла ошибка при создании кампании'
            });
        },
        complete: function() {
            submitBtn.prop('disabled', false).html('Создать кампанию');
        }
    });
});

// Обработка формы создания шаблона
$('#createTemplateForm').on('submit', function(e) {
    e.preventDefault();

    var formData = $(this).serialize();
    var submitBtn = $(this).find('button[type=submit]');

    submitBtn.prop('disabled', true).html('<i class=\"fas fa-spinner fa-spin\"></i> Создание...');

    $.ajax({
        url: '" . Url::to(['create-template']) . "',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                iziToast.success({
                    title: 'Успех',
                    message: response.message
                });
                $('#createTemplateModal').modal('hide');
                // Обновляем список шаблонов
                loadTemplates();
            } else {
                iziToast.error({
                    title: 'Ошибка',
                    message: response.message
                });
            }
        },
        error: function() {
            iziToast.error({
                title: 'Ошибка',
                message: 'Произошла ошибка при создании шаблона'
            });
        },
        complete: function() {
            submitBtn.prop('disabled', false).html('Создать шаблон');
        }
    });
});

// Загружаем шаблоны при загрузке страницы
$(document).ready(function() {
    loadTemplates();
});
");
?>