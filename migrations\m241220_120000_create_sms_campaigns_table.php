<?php

use yii\db\Migration;

/**
 * Создание таблицы для хранения SMS кампаний и статистики
 */
class m241220_120000_create_sms_campaigns_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Создаем таблицу SMS кампаний
        $this->createTable('{{%sms_campaigns}}', [
            'id' => $this->primaryKey(),
            'name' => $this->string(255)->notNull()->comment('Название кампании'),
            'message' => $this->text()->notNull()->comment('Текст сообщения'),
            'template_used' => $this->boolean()->defaultValue(true)->comment('Использовался ли шаблон'),
            'template_text' => $this->text()->comment('Текст шаблона (если использовался)'),
            'total_recipients' => $this->integer()->defaultValue(0)->comment('Общее количество получателей'),
            'sent_count' => $this->integer()->defaultValue(0)->comment('Количество отправленных SMS'),
            'delivered_count' => $this->integer()->defaultValue(0)->comment('Количество доставленных SMS'),
            'failed_count' => $this->integer()->defaultValue(0)->comment('Количество неудачных отправок'),
            'status' => $this->smallInteger()->defaultValue(0)->comment('Статус кампании: 0-создана, 1-обрабатывается, 2-завершена, 3-отменена, 4-ошибка'),
            'eskiz_campaign_id' => $this->string(100)->comment('ID кампании в Eskiz'),
            'filters_used' => $this->json()->comment('Использованные фильтры (JSON)'),
            'created_by' => $this->integer()->comment('Кто создал кампанию'),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'started_at' => $this->timestamp()->null()->comment('Время начала отправки'),
            'completed_at' => $this->timestamp()->null()->comment('Время завершения'),
        ]);

        // Создаем индексы
        $this->createIndex('idx_sms_campaigns_status', '{{%sms_campaigns}}', 'status');
        $this->createIndex('idx_sms_campaigns_created_at', '{{%sms_campaigns}}', 'created_at');
        $this->createIndex('idx_sms_campaigns_eskiz_id', '{{%sms_campaigns}}', 'eskiz_campaign_id');

        // Создаем таблицу для детальной статистики по получателям
        $this->createTable('{{%sms_campaign_recipients}}', [
            'id' => $this->primaryKey(),
            'campaign_id' => $this->integer()->notNull()->comment('ID кампании'),
            'client_id' => $this->integer()->comment('ID клиента'),
            'phone_number' => $this->string(20)->notNull()->comment('Номер телефона'),
            'client_name' => $this->string(255)->comment('Имя клиента'),
            'status' => $this->smallInteger()->defaultValue(0)->comment('Статус отправки: 0-ожидание, 1-отправлено, 2-доставлено, 3-ошибка'),
            'eskiz_message_id' => $this->string(100)->comment('ID сообщения в Eskiz'),
            'error_message' => $this->text()->comment('Сообщение об ошибке'),
            'sent_at' => $this->timestamp()->null()->comment('Время отправки'),
            'delivered_at' => $this->timestamp()->null()->comment('Время доставки'),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
        ]);

        // Создаем индексы для таблицы получателей
        $this->createIndex('idx_sms_recipients_campaign', '{{%sms_campaign_recipients}}', 'campaign_id');
        $this->createIndex('idx_sms_recipients_client', '{{%sms_campaign_recipients}}', 'client_id');
        $this->createIndex('idx_sms_recipients_phone', '{{%sms_campaign_recipients}}', 'phone_number');
        $this->createIndex('idx_sms_recipients_status', '{{%sms_campaign_recipients}}', 'status');

        // Создаем внешние ключи
        $this->addForeignKey(
            'fk_sms_recipients_campaign',
            '{{%sms_campaign_recipients}}',
            'campaign_id',
            '{{%sms_campaigns}}',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk_sms_recipients_client',
            '{{%sms_campaign_recipients}}',
            'client_id',
            '{{%clients}}',
            'id',
            'SET NULL',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk_sms_campaigns_created_by',
            '{{%sms_campaigns}}',
            'created_by',
            '{{%users}}',
            'id',
            'SET NULL',
            'CASCADE'
        );

        // Создаем таблицу для хранения шаблонов
        $this->createTable('{{%sms_templates}}', [
            'id' => $this->primaryKey(),
            'name' => $this->string(255)->notNull()->comment('Название шаблона'),
            'template_text' => $this->text()->notNull()->comment('Текст шаблона'),
            'status' => $this->smallInteger()->defaultValue(0)->comment('Статус: 0-на модерации, 1-одобрен, 2-отклонен'),
            'eskiz_template_id' => $this->string(100)->comment('ID шаблона в Eskiz'),
            'is_default' => $this->boolean()->defaultValue(false)->comment('Шаблон по умолчанию'),
            'created_by' => $this->integer()->comment('Кто создал шаблон'),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'approved_at' => $this->timestamp()->null()->comment('Время одобрения'),
        ]);

        // Создаем индексы для шаблонов
        $this->createIndex('idx_sms_templates_status', '{{%sms_templates}}', 'status');
        $this->createIndex('idx_sms_templates_default', '{{%sms_templates}}', 'is_default');

        $this->addForeignKey(
            'fk_sms_templates_created_by',
            '{{%sms_templates}}',
            'created_by',
            '{{%users}}',
            'id',
            'SET NULL',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Удаляем внешние ключи
        $this->dropForeignKey('fk_sms_recipients_campaign', '{{%sms_campaign_recipients}}');
        $this->dropForeignKey('fk_sms_recipients_client', '{{%sms_campaign_recipients}}');
        $this->dropForeignKey('fk_sms_campaigns_created_by', '{{%sms_campaigns}}');
        $this->dropForeignKey('fk_sms_templates_created_by', '{{%sms_templates}}');

        // Удаляем таблицы
        $this->dropTable('{{%sms_campaign_recipients}}');
        $this->dropTable('{{%sms_campaigns}}');
        $this->dropTable('{{%sms_templates}}');
    }
}
