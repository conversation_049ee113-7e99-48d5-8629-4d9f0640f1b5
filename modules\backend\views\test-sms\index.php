<?php

use yii\helpers\Html;
use yii\helpers\Url;

$this->title = 'Тест массовой отправки SMS';
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="test-sms-index">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-sms"></i> <?= Html::encode($this->title) ?>
                    </h3>
                </div>
                <div class="card-body">
                    
                    <!-- Статус API -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="alert <?= $serviceAvailable ? 'alert-success' : 'alert-danger' ?>">
                                <h5>
                                    <i class="icon fas <?= $serviceAvailable ? 'fa-check' : 'fa-times' ?>"></i>
                                    Статус API Eskiz
                                </h5>
                                <?= $serviceAvailable ? 'API доступен' : 'API недоступен' ?>
                                <button type="button" class="btn btn-sm btn-outline-secondary ml-2" onclick="checkApi()">
                                    Проверить
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <h5><i class="icon fas fa-users"></i> Клиенты в базе</h5>
                                Найдено активных клиентов с телефонами: <strong><?= count($clients) ?></strong>
                            </div>
                        </div>
                    </div>

                    <!-- Список клиентов -->
                    <?php if (!empty($clients)): ?>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <h5>Клиенты для тестирования:</h5>
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Имя</th>
                                            <th>Телефон</th>
                                            <th>Статус</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($clients as $client): ?>
                                        <tr>
                                            <td><?= $client->id ?></td>
                                            <td><?= Html::encode($client->full_name ?: 'Не указано') ?></td>
                                            <td><?= Html::encode($client->phone_number) ?></td>
                                            <td>
                                                <span class="badge badge-success">Активен</span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Форма отправки -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Тестовая отправка SMS</h5>
                                </div>
                                <div class="card-body">
                                    <form id="sms-test-form">
                                        <div class="form-group">
                                            <label for="message">Текст сообщения:</label>
                                            <textarea 
                                                class="form-control" 
                                                id="message" 
                                                name="message" 
                                                rows="3" 
                                                placeholder="Введите текст сообщения для отправки..."
                                                required>Тестовое сообщение от системы CARPET. Время: <?= date('Y-m-d H:i:s') ?></textarea>
                                            <small class="form-text text-muted">
                                                Сообщение будет отправлено всем активным клиентам из списка выше.
                                            </small>
                                        </div>
                                        
                                        <div class="form-group">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="use_template" name="use_template" checked>
                                                <label class="custom-control-label" for="use_template">
                                                    Использовать подтвержденный шаблон
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">
                                                Если включено, сообщение будет обернуто в подтвержденный шаблон из Eskiz.
                                            </small>
                                        </div>

                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary" <?= !$serviceAvailable || empty($clients) ? 'disabled' : '' ?>>
                                                <i class="fas fa-paper-plane"></i> Отправить тестовые SMS
                                            </button>
                                            <button type="button" class="btn btn-secondary ml-2" onclick="checkApi()">
                                                <i class="fas fa-sync"></i> Проверить API
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Результат</h5>
                                </div>
                                <div class="card-body">
                                    <div id="result-container">
                                        <p class="text-muted">Результат отправки появится здесь...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('sms-test-form');
    const resultContainer = document.getElementById('result-container');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        
        // Показываем загрузку
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Отправляем...';
        resultContainer.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> Отправка SMS...</div>';
        
        fetch('<?= Url::to(['test-bulk-sms']) ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resultContainer.innerHTML = `
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check"></i> Успешно!</h6>
                        <p><strong>Сообщение:</strong> ${data.message}</p>
                        <p><strong>Отправлено:</strong> ${data.sent_count} из ${data.recipients_count} SMS</p>
                        ${data.recipients ? `<p><strong>Получатели:</strong><br>${data.recipients.join('<br>')}</p>` : ''}
                        ${data.api_result ? `<details><summary>Ответ API</summary><pre>${JSON.stringify(data.api_result, null, 2)}</pre></details>` : ''}
                    </div>
                `;
            } else {
                resultContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-times"></i> Ошибка!</h6>
                        <p>${data.message}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            resultContainer.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-times"></i> Ошибка!</h6>
                    <p>Произошла ошибка при отправке: ${error.message}</p>
                </div>
            `;
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Отправить тестовые SMS';
        });
    });
});

function checkApi() {
    const resultContainer = document.getElementById('result-container');
    resultContainer.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> Проверяем API...</div>';
    
    fetch('<?= Url::to(['check-api']) ?>')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultContainer.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check"></i> API доступен!</h6>
                    <p><strong>Токен:</strong> ${data.token_preview}</p>
                    <p><strong>Шаблон:</strong> ${data.approved_template}</p>
                </div>
            `;
        } else {
            resultContainer.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-times"></i> API недоступен!</h6>
                    <p>${data.message}</p>
                </div>
            `;
        }
    })
    .catch(error => {
        resultContainer.innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-times"></i> Ошибка!</h6>
                <p>Произошла ошибка при проверке API: ${error.message}</p>
            </div>
        `;
    });
}
</script>
